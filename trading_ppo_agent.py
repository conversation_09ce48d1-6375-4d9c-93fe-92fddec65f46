import torch
import numpy as np
import pandas as pd
from collections import deque
from coin_game import CoinGameAI, Action
from ppo_model import PPOActorCritic
from ppo_trainer import PPOTrainer
from helper import plot

# PPO Hyperparameters for trading
LEARNING_RATE = 3e-4
GAMMA = 0.99
EPS_CLIP = 0.2
K_EPOCHS = 4
UPDATE_TIMESTEP = 1024  # Update policy every n timesteps
MAX_EPISODES = 500

class TradingPPOAgent:
    def __init__(self):
        self.n_games = 0
        self.memory = []  # Store trajectory data
        self.timestep = 0

        # Initialize model and trainer
        self.model = PPOActorCritic(20, 256, 3)  # 20 state features, 3 actions
        self.trainer = PPOTrainer(
            self.model,
            lr=LEARNING_RATE,
            gamma=GAMMA,
            eps_clip=EPS_CLIP,
            k_epochs=K_EPOCHS
        )

        # For tracking performance
        self.episode_profits = []
        self.episode_rewards = []

    def get_state(self, game):
        """Get state from the trading game"""
        return game.get_state()

    def get_action(self, state):
        """Get action from PPO policy"""
        state_tensor = torch.FloatTensor(state).unsqueeze(0)

        with torch.no_grad():
            action, log_prob, action_probs = self.model.get_action(state_tensor)
            _, value = self.model.get_action_and_value(state_tensor)

        return action, log_prob.item(), value.item()

    def store_transition(self, state, action, log_prob, reward, done, value):
        """Store transition in memory"""
        self.memory.append({
            'state': state,
            'action': action,
            'log_prob': log_prob,
            'reward': reward,
            'done': done,
            'value': value
        })

    def update_policy(self):
        """Update policy using PPO"""
        if len(self.memory) == 0:
            return {}

        # Extract data from memory
        states = [transition['state'] for transition in self.memory]
        actions = [transition['action'] for transition in self.memory]
        log_probs = [transition['log_prob'] for transition in self.memory]
        rewards = [transition['reward'] for transition in self.memory]
        dones = [transition['done'] for transition in self.memory]
        values = [transition['value'] for transition in self.memory]

        # Train the model
        loss_info = self.trainer.train_step(states, actions, log_probs,
                                          rewards, dones, values)

        # Clear memory
        self.memory = []

        return loss_info

    def action_to_move(self, action):
        """Convert action index to move format"""
        final_move = [0, 0, 0]
        final_move[action] = 1
        return final_move


def load_crypto_data():
    """Load cryptocurrency data from CSV file"""
    try:
        # Load the CSV file
        df = pd.read_csv('data/2AWpaMVcGbXy1rJQ4RX2cfXsKvRe2baQCKm9dCrdpump_1m.csv')

        # Rename columns to match expected format
        df = df.rename(columns={
            'o': 'open',
            'h': 'high',
            'l': 'low',
            'c': 'close',
            'v': 'volume'
        })

        # Select only the OHLCV columns we need
        df = df[['open', 'high', 'low', 'close', 'volume']].copy()

        # Remove any rows with missing data
        df = df.dropna()

        print(f"Loaded {len(df)} periods of real crypto data")
        print(f"Price range: ${df['close'].min():.6f} - ${df['close'].max():.6f}")

        return df

    except FileNotFoundError:
        print("Crypto data file not found, creating sample data...")
        return create_sample_crypto_data()
    except Exception as e:
        print(f"Error loading crypto data: {e}, creating sample data...")
        return create_sample_crypto_data()

def create_sample_crypto_data():
    """Create sample cryptocurrency data with more realistic patterns (fallback)"""
    np.random.seed(42)
    n_periods = 3000

    # Generate more complex price patterns
    prices = [100.0]  # Start at $100

    for i in range(n_periods):
        # Multiple time scale patterns
        long_trend = 0.0002 * np.sin(i / 200)  # Long-term trend
        medium_cycle = 0.001 * np.sin(i / 50)  # Medium-term cycle
        short_noise = np.random.normal(0, 0.02)  # Short-term noise

        # Occasional volatility spikes
        if np.random.random() < 0.01:  # 1% chance of volatility spike
            short_noise *= 3

        total_return = long_trend + medium_cycle + short_noise
        new_price = prices[-1] * (1 + total_return)
        prices.append(max(new_price, 0.01))  # Prevent negative prices

    # Create OHLCV data
    data = []
    for i in range(len(prices)-1):
        open_price = prices[i]
        close_price = prices[i+1]

        # Generate realistic high/low with some randomness
        volatility = abs(np.random.normal(0, 0.005))
        high = max(open_price, close_price) * (1 + volatility)
        low = min(open_price, close_price) * (1 - volatility)
        volume = np.random.randint(50000, 200000)

        data.append({
            'open': open_price,
            'high': high,
            'low': low,
            'close': close_price,
            'volume': volume
        })

    return pd.DataFrame(data)


def train_trading_ppo():
    """Train the PPO trading agent"""
    print("Loading cryptocurrency data...")
    df = load_crypto_data()

    plot_scores = []
    plot_mean_scores = []
    total_score = 0
    record = -float('inf')
    agent = TradingPPOAgent()

    episode_reward = 0
    episode_length = 0

    print("Starting PPO Trading Training...")

    while agent.n_games < MAX_EPISODES:
        # Create new game instance
        game = CoinGameAI(df)
        episode_profit = 0
        episode_reward = 0
        episode_length = 0

        while True:
            # Get current state
            state = agent.get_state(game)

            # Get action from policy
            action, log_prob, value = agent.get_action(state)
            final_move = agent.action_to_move(action)

            # Perform action
            reward, done, profit = game.play_step(final_move)
            episode_reward += reward
            episode_length += 1
            agent.timestep += 1

            # Store transition
            agent.store_transition(state, action, log_prob, reward, done, value)

            if done:
                # Episode finished
                agent.n_games += 1
                episode_profit = profit

                # Track performance
                agent.episode_profits.append(episode_profit)
                agent.episode_rewards.append(episode_reward)

                if episode_profit > record:
                    record = episode_profit
                    agent.model.save('trading_ppo_model.pth')

                print(f'Game {agent.n_games}, Profit: ${episode_profit:.4f}, '
                      f'Episode Reward: {episode_reward:.2f}, Record: ${record:.4f}, '
                      f'Length: {episode_length}, Trades: {len(game.trades)}')

                plot_scores.append(episode_profit)
                total_score += episode_profit
                mean_score = total_score / agent.n_games
                plot_mean_scores.append(mean_score)
                plot(plot_scores, plot_mean_scores)

                break

        # Update policy every UPDATE_TIMESTEP steps
        if agent.timestep % UPDATE_TIMESTEP == 0:
            loss_info = agent.update_policy()
            if loss_info:
                print(f"Policy updated at timestep {agent.timestep}")
                print(f"Losses - Actor: {loss_info['actor_loss']:.4f}, "
                      f"Critic: {loss_info['critic_loss']:.4f}, "
                      f"Entropy: {loss_info['entropy_loss']:.4f}")


def test_trading_ppo():
    """Test the trained PPO agent"""
    print("Testing trained PPO trading agent...")

    # Load trained model
    agent = TradingPPOAgent()
    try:
        agent.model.load('trading_ppo_model.pth')
        print("Loaded trained PPO model")
    except:
        print("No trained PPO model found, using random agent")

    # Load test data
    df = load_crypto_data()
    game = CoinGameAI(df)

    total_profit = 0
    episode_length = 0

    print("Running test episode...")

    while True:
        state = agent.get_state(game)

        # Use trained policy (no exploration)
        action, _, _ = agent.get_action(state)
        final_move = agent.action_to_move(action)

        reward, done, profit = game.play_step(final_move)
        episode_length += 1

        if done:
            total_profit = profit
            break

    print(f"\nPPO Test Results:")
    print(f"Final Profit: ${total_profit:.4f}")
    print(f"Episode Length: {episode_length}")
    print(f"Number of Buy Signals: {len(game.buy_points)}")
    print(f"Number of Sell Signals: {len(game.sell_points)}")
    print(f"Total Trades: {len(game.trades)}")

    # Calculate some trading metrics
    if len(game.trades) > 0:
        buy_trades = [t for t in game.trades if t['action'] == 'BUY']
        sell_trades = [t for t in game.trades if t['action'] == 'SELL']

        print(f"Buy Trades: {len(buy_trades)}")
        print(f"Sell Trades: {len(sell_trades)}")

        if len(sell_trades) > 0:
            profitable_trades = [t for t in sell_trades if t.get('pnl', 0) > 0]
            print(f"Profitable Trades: {len(profitable_trades)}/{len(sell_trades)} "
                  f"({len(profitable_trades)/len(sell_trades)*100:.1f}%)")

    # Print recent trades
    if game.trades:
        print(f"\nRecent Trade History:")
        for trade in game.trades[-10:]:
            if 'pnl' in trade:
                print(f"Step {trade['step']}: {trade['action']} at ${trade['price']:.4f}, "
                      f"P&L: ${trade['pnl']:.4f}")
            else:
                print(f"Step {trade['step']}: {trade['action']} at ${trade['price']:.4f}")


def compare_trading_agents():
    """Compare DQN vs PPO trading performance"""
    print("=== Comparing Trading Agents ===")

    # Test both agents on the same data
    df = load_crypto_data()

    # Test DQN agent
    print("\n--- Testing DQN Trading Agent ---")
    try:
        from trading_agent import TradingAgent
        dqn_agent = TradingAgent()
        dqn_agent.model.load_state_dict(torch.load('./model/trading_model.pth'))

        game = CoinGameAI(df)
        while True:
            state = dqn_agent.get_state(game)
            state_tensor = torch.tensor(state, dtype=torch.float)
            with torch.no_grad():
                prediction = dqn_agent.model(state_tensor)
                action_idx = torch.argmax(prediction).item()

            final_move = [0, 0, 0]
            final_move[action_idx] = 1

            reward, done, profit = game.play_step(final_move)
            if done:
                dqn_profit = profit
                dqn_trades = len(game.trades)
                break

        print(f"DQN Final Profit: ${dqn_profit:.4f}, Trades: {dqn_trades}")
    except Exception as e:
        print(f"DQN test failed: {e}")
        dqn_profit = 0
        dqn_trades = 0

    # Test PPO agent
    print("\n--- Testing PPO Trading Agent ---")
    try:
        ppo_agent = TradingPPOAgent()
        ppo_agent.model.load('trading_ppo_model.pth')

        game = CoinGameAI(df)
        while True:
            state = ppo_agent.get_state(game)
            action, _, _ = ppo_agent.get_action(state)
            final_move = ppo_agent.action_to_move(action)

            reward, done, profit = game.play_step(final_move)
            if done:
                ppo_profit = profit
                ppo_trades = len(game.trades)
                break

        print(f"PPO Final Profit: ${ppo_profit:.4f}, Trades: {ppo_trades}")
    except Exception as e:
        print(f"PPO test failed: {e}")
        ppo_profit = 0
        ppo_trades = 0

    # Compare results
    print(f"\n=== Comparison Results ===")
    print(f"DQN: ${dqn_profit:.4f} profit, {dqn_trades} trades")
    print(f"PPO: ${ppo_profit:.4f} profit, {ppo_trades} trades")

    if ppo_profit > dqn_profit:
        print("PPO performed better!")
    elif dqn_profit > ppo_profit:
        print("DQN performed better!")
    else:
        print("Both agents performed equally!")


if __name__ == '__main__':
    print("=== PPO Cryptocurrency Trading AI ===")
    print("1. Train PPO agent")
    print("2. Test trained PPO agent")
    print("3. Compare DQN vs PPO trading")

    choice = input("Choose option (1/2/3): ")

    if choice == "1":
        train_trading_ppo()
    elif choice == "2":
        test_trading_ppo()
    elif choice == "3":
        compare_trading_agents()
    else:
        print("Invalid choice!")
