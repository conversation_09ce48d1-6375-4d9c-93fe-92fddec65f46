import torch
import random
import numpy as np
import pandas as pd
from collections import deque
from coin_game import CoinGameAI, Action
from model import Linear_QNet, QTrainer
from helper import plot

# Trading specific parameters
MAX_MEMORY = 100_000
BATCH_SIZE = 1000
LR = 0.001

class TradingAgent:
    def __init__(self):
        self.n_games = 0
        self.epsilon = 0  # randomness
        self.gamma = 0.9  # discount rate
        self.memory = deque(maxlen=MAX_MEMORY)

        # Model: 20 state features -> 256 hidden -> 3 actions (BUY, SELL, HOLD)
        self.model = Linear_QNet(20, 256, 3)
        self.trainer = QTrainer(self.model, lr=LR, gamma=self.gamma)

    def get_state(self, game):
        """Get state from the trading game"""
        return game.get_state()

    def remember(self, state, action, reward, next_state, done):
        """Store experience in memory"""
        self.memory.append((state, action, reward, next_state, done))

    def train_long_memory(self):
        """Train on a batch of experiences"""
        if len(self.memory) > BATCH_SIZE:
            mini_sample = random.sample(self.memory, BATCH_SIZE)
        else:
            mini_sample = self.memory

        states, actions, rewards, next_states, dones = zip(*mini_sample)
        self.trainer.train_step(states, actions, rewards, next_states, dones)

    def train_short_memory(self, state, action, reward, next_state, done):
        """Train on single experience"""
        self.trainer.train_step(state, action, reward, next_state, done)

    def get_action(self, state):
        """Get action using epsilon-greedy policy"""
        # Exploration vs exploitation
        self.epsilon = 80 - self.n_games
        final_move = [0, 0, 0]

        if random.randint(0, 200) < self.epsilon:
            # Random action
            move = random.randint(0, 2)
            final_move[move] = 1
        else:
            # Model prediction
            state0 = torch.tensor(state, dtype=torch.float)
            prediction = self.model(state0)
            move = torch.argmax(prediction).item()
            final_move[move] = 1

        return final_move


def load_crypto_data():
    """Load cryptocurrency data from CSV file"""
    try:
        # Load the CSV file
        df = pd.read_csv('data/2AWpaMVcGbXy1rJQ4RX2cfXsKvRe2baQCKm9dCrdpump_1m.csv')

        # Rename columns to match expected format
        df = df.rename(columns={
            'o': 'open',
            'h': 'high',
            'l': 'low',
            'c': 'close',
            'v': 'volume'
        })

        # Select only the OHLCV columns we need
        df = df[['open', 'high', 'low', 'close', 'volume']].copy()

        # Remove any rows with missing data
        df = df.dropna()

        print(f"Loaded {len(df)} periods of real crypto data")
        print(f"Price range: ${df['close'].min():.6f} - ${df['close'].max():.6f}")

        return df

    except FileNotFoundError:
        print("Crypto data file not found, creating sample data...")
        return create_sample_data()
    except Exception as e:
        print(f"Error loading crypto data: {e}, creating sample data...")
        return create_sample_data()

def create_sample_data():
    """Create sample cryptocurrency data for testing (fallback)"""
    np.random.seed(42)
    n_periods = 2000

    # Generate realistic crypto price movement with trends
    base_trend = 0.0001  # Slight upward trend
    volatility = 0.03

    returns = []
    for i in range(n_periods):
        # Add some cyclical patterns
        cycle = 0.001 * np.sin(i / 50)  # 50-period cycle
        noise = np.random.normal(0, volatility)
        returns.append(base_trend + cycle + noise)

    # Generate prices starting from $100
    prices = [100.0]
    for ret in returns:
        prices.append(prices[-1] * (1 + ret))

    # Create OHLCV data
    data = []
    for i in range(len(prices)-1):
        open_price = prices[i]
        close_price = prices[i+1]

        # Generate realistic high/low
        high_factor = 1 + abs(np.random.normal(0, 0.005))
        low_factor = 1 - abs(np.random.normal(0, 0.005))

        high = max(open_price, close_price) * high_factor
        low = min(open_price, close_price) * low_factor
        volume = np.random.randint(10000, 100000)

        data.append({
            'open': open_price,
            'high': high,
            'low': low,
            'close': close_price,
            'volume': volume
        })

    return pd.DataFrame(data)


def train_trading_agent():
    """Train the trading agent"""
    print("Loading cryptocurrency data...")
    df = load_crypto_data()

    plot_scores = []
    plot_mean_scores = []
    total_score = 0
    record = -float('inf')  # Can have negative profits
    agent = TradingAgent()

    print("Starting training...")

    while agent.n_games < 1000:  # Train for 1000 episodes
        # Create new game instance for each episode
        game = CoinGameAI(df)
        episode_reward = 0

        while True:
            # Get current state
            state_old = agent.get_state(game)

            # Get action
            final_move = agent.get_action(state_old)

            # Perform move
            reward, done, profit = game.play_step(final_move)
            state_new = agent.get_state(game)

            episode_reward += reward

            # Train short memory
            agent.train_short_memory(state_old, final_move, reward, state_new, done)

            # Remember
            agent.remember(state_old, final_move, reward, state_new, done)

            if done:
                # Episode finished
                game.reset()
                agent.n_games += 1
                agent.train_long_memory()

                # Track performance
                final_profit = profit

                if final_profit > record:
                    record = final_profit
                    agent.model.save('trading_model.pth')

                print(f'Game {agent.n_games}, Final Profit: ${final_profit:.4f}, '
                      f'Episode Reward: {episode_reward:.2f}, Record: ${record:.4f}')

                plot_scores.append(final_profit)
                total_score += final_profit
                mean_score = total_score / agent.n_games
                plot_mean_scores.append(mean_score)
                plot(plot_scores, plot_mean_scores)

                break


def test_trained_agent():
    """Test the trained agent"""
    print("Testing trained agent...")

    # Load trained model
    agent = TradingAgent()
    try:
        agent.model.load_state_dict(torch.load('./model/trading_model.pth'))
        print("Loaded trained model")
    except:
        print("No trained model found, using random agent")

    # Load test data
    df = load_crypto_data()
    game = CoinGameAI(df)

    total_profit = 0
    trades_made = 0

    print("Running test episode...")

    while True:
        state = agent.get_state(game)

        # Use trained policy (no exploration)
        state_tensor = torch.tensor(state, dtype=torch.float)
        with torch.no_grad():
            prediction = agent.model(state_tensor)
            action_idx = torch.argmax(prediction).item()

        final_move = [0, 0, 0]
        final_move[action_idx] = 1

        reward, done, profit = game.play_step(final_move)

        # Count trades
        if action_idx in [0, 1]:  # BUY or SELL
            trades_made += 1

        if done:
            total_profit = profit
            break

    print(f"\nTest Results:")
    print(f"Final Profit: ${total_profit:.4f}")
    print(f"Total Trades Made: {trades_made}")
    print(f"Number of Buy Signals: {len(game.buy_points)}")
    print(f"Number of Sell Signals: {len(game.sell_points)}")

    # Print trade history and fees
    if game.trades:
        print(f"\nTrade History:")
        total_fees = 0
        for trade in game.trades[-10:]:  # Show last 10 trades
            fee = trade.get('fee', 0)
            total_fees += fee
            if 'fee' in trade:
                print(f"Step {trade['step']}: {trade['action']} at ${trade['price']:.6f}, Fee: ${fee:.4f}")
            else:
                print(f"Step {trade['step']}: {trade['action']} at ${trade['price']:.6f}")

        print(f"\nTotal fees paid: ${sum(trade.get('fee', 0) for trade in game.trades):.4f}")
        print(f"Fee structure: {game.fee_percentage*100:.1f}% + ${game.fee_fixed:.3f}")


if __name__ == '__main__':
    print("=== Cryptocurrency Trading AI ===")
    print("1. Train agent")
    print("2. Test trained agent")
    print("3. Create sample data and run manual test")

    choice = input("Choose option (1/2/3): ")

    if choice == "1":
        train_trading_agent()
    elif choice == "2":
        test_trained_agent()
    elif choice == "3":
        # Manual test with real data
        df = load_crypto_data()
        game = CoinGameAI(df)

        print("Manual test - press keys:")
        print("1 - BUY, 2 - SELL, 3 - HOLD, q - quit")

        while True:
            state = game.get_state()
            print(f"Step: {game.current_step}, Price: ${game.get_current_price():.4f}, "
                  f"Position: {game.position.name}, Balance: ${game.balance:.4f}")

            action_input = input("Action (1/2/3/q): ")

            if action_input == 'q':
                break

            try:
                action_idx = int(action_input) - 1
                if action_idx in [0, 1, 2]:
                    final_move = [0, 0, 0]
                    final_move[action_idx] = 1

                    reward, done, profit = game.play_step(final_move)
                    print(f"Reward: {reward:.2f}, Profit: ${profit:.4f}")

                    if done:
                        print(f"Game finished! Final profit: ${profit:.4f}")
                        break
            except:
                print("Invalid input")
    else:
        print("Invalid choice!")
