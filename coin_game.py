import pygame
import pandas as pd
import numpy as np
from enum import Enum
from collections import namedtuple

pygame.init()
font = pygame.font.Font('arial.ttf', 20)

class Action(Enum):
    BUY = 0
    SELL = 1
    HOLD = 2

class Position(Enum):
    NONE = 0
    LONG = 1

# Colors
WHITE = (255, 255, 255)
BLACK = (0, 0, 0)
GREEN = (0, 255, 0)
RED = (255, 0, 0)
BLUE = (0, 0, 255)
GRAY = (128, 128, 128)
YELLOW = (255, 255, 0)

class CoinGameAI:
    def __init__(self, df, w=1200, h=800):
        """
        Initialize trading game
        Args:
            df: DataFrame with OHLCV data (columns: open, high, low, close, volume)
            w, h: window dimensions
        """
        self.df = df.copy()
        self.w = w
        self.h = h

        # Initialize display
        self.display = pygame.display.set_mode((self.w, self.h))
        pygame.display.set_caption('Crypto Trading AI')
        self.clock = pygame.time.Clock()

        # Game parameters
        self.initial_balance = 1.01  # Start with $1.01 to cover fees
        self.risk_amount = 1.0       # Always risk $1

        # Transaction fees
        self.fee_percentage = 0.005  # 0.5%
        self.fee_fixed = 0.001       # $0.001

        # Trading history
        self.trades = []  # List of (timestamp, action, price, balance)
        self.buy_points = []
        self.sell_points = []

        self.reset()

    def reset(self):
        """Reset game state"""
        self.current_step = 0
        self.balance = self.initial_balance
        self.position = Position.NONE
        self.entry_price = 0
        self.coins_held = 0
        self.total_profit = 0
        self.trades = []
        self.buy_points = []
        self.sell_points = []
        self.frame_iteration = 0

        # For visualization
        self.chart_start_idx = 0
        self.chart_window = min(100, len(self.df))  # Show last 100 candles

    def get_current_price(self):
        """Get current close price"""
        if self.current_step >= len(self.df):
            return self.df.iloc[-1]['close']
        return self.df.iloc[self.current_step]['close']

    def calculate_transaction_fee(self, transaction_value):
        """Calculate transaction fee: 0.5% + $0.001"""
        percentage_fee = transaction_value * self.fee_percentage
        total_fee = percentage_fee + self.fee_fixed
        return total_fee

    def get_state(self):
        """Get current state for AI agent"""
        if self.current_step >= len(self.df):
            return np.zeros(20)  # Return zeros if we're at the end

        # Look back window
        lookback = 20
        start_idx = max(0, self.current_step - lookback + 1)
        end_idx = self.current_step + 1

        # Price features
        prices = self.df.iloc[start_idx:end_idx]['close'].values
        if len(prices) < lookback:
            # Pad with first price if not enough history
            prices = np.pad(prices, (lookback - len(prices), 0), 'edge')

        # Normalize prices (percentage change from first price)
        if prices[0] != 0:
            normalized_prices = (prices - prices[0]) / prices[0]
        else:
            normalized_prices = np.zeros_like(prices)

        # Technical indicators
        current_price = self.get_current_price()

        # Simple moving averages
        sma_5 = np.mean(prices[-5:]) if len(prices) >= 5 else current_price
        sma_10 = np.mean(prices[-10:]) if len(prices) >= 10 else current_price

        # Price relative to moving averages
        price_vs_sma5 = (current_price - sma_5) / sma_5 if sma_5 != 0 else 0
        price_vs_sma10 = (current_price - sma_10) / sma_10 if sma_10 != 0 else 0

        # Position and balance features
        position_feature = 1.0 if self.position == Position.LONG else 0.0
        balance_feature = self.balance / self.initial_balance

        # Profit if we were to close position now
        unrealized_pnl = 0
        if self.position == Position.LONG and self.entry_price > 0:
            unrealized_pnl = (current_price - self.entry_price) / self.entry_price

        state = np.array([
            # Price momentum (last 5 normalized prices)
            *normalized_prices[-5:],
            # Technical indicators
            price_vs_sma5,
            price_vs_sma10,
            # Position info
            position_feature,
            balance_feature,
            unrealized_pnl,
            # Market features
            (current_price - prices[-2]) / prices[-2] if len(prices) >= 2 and prices[-2] != 0 else 0,  # Last price change
        ])

        # Pad to fixed size if needed
        if len(state) < 20:
            state = np.pad(state, (0, 20 - len(state)), 'constant')

        return state[:20]  # Ensure exactly 20 features

    def play_step(self, action):
        """Execute one step of the game"""
        self.frame_iteration += 1

        # Handle pygame events
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                pygame.quit()
                quit()

        # Check if game is over (reached end of data)
        if self.current_step >= len(self.df) - 1:
            # Force close any open position
            if self.position == Position.LONG:
                self._execute_sell()
            return self._calculate_reward(Action.HOLD), True, self.total_profit

        # Execute action
        reward = 0
        current_price = self.get_current_price()

        if np.array_equal(action, [1, 0, 0]):  # BUY
            if self.position == Position.NONE:
                reward = self._execute_buy(current_price)
        elif np.array_equal(action, [0, 1, 0]):  # SELL
            if self.position == Position.LONG:
                reward = self._execute_sell()
        else:  # HOLD
            reward = self._calculate_reward(Action.HOLD)

        # Move to next time step
        self.current_step += 1

        # Update visualization
      #  self._update_ui()
      #  self.clock.tick(500)  # Slower than snake game for better visualization

        return reward, False, self.total_profit

    def _execute_buy(self, price):
        """Execute buy order"""
        if self.position != Position.NONE:
            return -1  # Penalty for invalid action

        # Calculate transaction fee for buying $1 worth
        transaction_fee = self.calculate_transaction_fee(self.risk_amount)

        # Check if we have enough balance to cover the purchase + fee
        total_cost = self.risk_amount + transaction_fee
        if self.balance < total_cost:
            return -1  # Penalty for insufficient funds

        # Execute buy order
        net_investment = self.risk_amount - transaction_fee  # Amount actually invested after fee
        self.coins_held = net_investment / price
        self.entry_price = price
        self.position = Position.LONG

        # Deduct total cost from balance
        self.balance -= total_cost

        # Record trade
        self.trades.append({
            'step': self.current_step,
            'action': 'BUY',
            'price': price,
            'amount': self.coins_held,
            'balance': self.balance,
            'fee': transaction_fee,
            'net_investment': net_investment
        })
        self.buy_points.append((self.current_step, price))

        return 0.1  # Small positive reward for taking action

    def _execute_sell(self):
        """Execute sell order"""
        if self.position != Position.LONG:
            return -1  # Penalty for invalid action

        current_price = self.get_current_price()

        # Calculate gross sell value
        gross_sell_value = self.coins_held * current_price

        # Calculate transaction fee for selling
        transaction_fee = self.calculate_transaction_fee(gross_sell_value)

        # Net proceeds after fee
        net_sell_value = gross_sell_value - transaction_fee

        # Calculate profit/loss relative to original $1 investment
        # Note: We already paid fee when buying, so this is net vs net
        original_investment = self.risk_amount  # Original $1 investment
        pnl = net_sell_value - original_investment

        # Update balance based on trading rules
        # Add back the net proceeds to balance
        self.balance += net_sell_value

        # Track total profit/loss
        self.total_profit += pnl

        # Reset balance for next trade based on profit/loss
        if pnl > 0:  # Profit - keep profit safe, reset to initial balance for next trade
            self.balance = self.initial_balance  # Reset to initial balance for next trade
            # Note: profit is tracked in total_profit
        else:  # Loss - reduce available balance for next trade
            # If we lost money, we have less available for next trade
            # But ensure we always have at least enough for one more trade
            min_balance_needed = self.risk_amount + self.calculate_transaction_fee(self.risk_amount)
            self.balance = max(self.balance, min_balance_needed)

        # Record trade
        self.trades.append({
            'step': self.current_step,
            'action': 'SELL',
            'price': current_price,
            'amount': self.coins_held,
            'balance': self.balance,
            'pnl': pnl,
            'fee': transaction_fee,
            'gross_value': gross_sell_value,
            'net_value': net_sell_value
        })
        self.sell_points.append((self.current_step, current_price))

        # Reset position
        self.position = Position.NONE
        self.coins_held = 0
        self.entry_price = 0

        # Reward based on profit/loss
        return pnl * 10  # Scale reward

    def _calculate_reward(self, action):
        """Calculate reward for current state"""
        if action == Action.HOLD:
            if self.position == Position.LONG:
                # Small penalty for holding (encourage action)
                current_price = self.get_current_price()
                unrealized_pnl = (current_price - self.entry_price) / self.entry_price
                return unrealized_pnl * 0.1  # Small reward based on unrealized P&L
            return -0.01  # Small penalty for holding without position
        return 0

    def _update_ui(self):
        """Update the game display"""
        self.display.fill(WHITE)

        # Calculate chart area
        chart_area = pygame.Rect(50, 50, self.w - 100, self.h - 200)

        # Draw price chart
        self._draw_price_chart(chart_area)

        # Draw info panel
        self._draw_info_panel()

        pygame.display.flip()

    def _draw_price_chart(self, chart_area):
        """Draw the price chart with current position"""
        if len(self.df) == 0:
            return

        # Determine visible range
        end_idx = min(self.current_step + 1, len(self.df))
        start_idx = max(0, end_idx - self.chart_window)

        visible_data = self.df.iloc[start_idx:end_idx]
        if len(visible_data) == 0:
            return

        # Price range
        price_min = visible_data['low'].min()
        price_max = visible_data['high'].max()
        price_range = price_max - price_min
        if price_range == 0:
            price_range = 1

        # Draw candlesticks
        candle_width = max(1, chart_area.width // len(visible_data))

        for i, (_, row) in enumerate(visible_data.iterrows()):
            x = chart_area.left + i * candle_width

            # Normalize prices to chart area
            open_y = chart_area.bottom - ((row['open'] - price_min) / price_range) * chart_area.height
            close_y = chart_area.bottom - ((row['close'] - price_min) / price_range) * chart_area.height
            high_y = chart_area.bottom - ((row['high'] - price_min) / price_range) * chart_area.height
            low_y = chart_area.bottom - ((row['low'] - price_min) / price_range) * chart_area.height

            # Draw high-low line
            pygame.draw.line(self.display, BLACK, (x + candle_width//2, high_y), (x + candle_width//2, low_y), 1)

            # Draw open-close rectangle
            color = GREEN if row['close'] >= row['open'] else RED
            rect_height = abs(close_y - open_y)
            rect_y = min(open_y, close_y)
            pygame.draw.rect(self.display, color, (x, rect_y, candle_width-1, max(1, rect_height)))

        # Draw buy/sell points
        for step, price in self.buy_points:
            if start_idx <= step < end_idx:
                x = chart_area.left + (step - start_idx) * candle_width + candle_width//2
                y = chart_area.bottom - ((price - price_min) / price_range) * chart_area.height
                pygame.draw.circle(self.display, BLUE, (int(x), int(y)), 5)
                # Draw "B" text
                text = font.render("B", True, BLUE)
                self.display.blit(text, (x-5, y-20))

        for step, price in self.sell_points:
            if start_idx <= step < end_idx:
                x = chart_area.left + (step - start_idx) * candle_width + candle_width//2
                y = chart_area.bottom - ((price - price_min) / price_range) * chart_area.height
                pygame.draw.circle(self.display, RED, (int(x), int(y)), 5)
                # Draw "S" text
                text = font.render("S", True, RED)
                self.display.blit(text, (x-5, y-20))

        # Draw current position marker
        if self.current_step < len(self.df) and start_idx <= self.current_step < end_idx:
            current_price = self.get_current_price()
            x = chart_area.left + (self.current_step - start_idx) * candle_width + candle_width//2
            y = chart_area.bottom - ((current_price - price_min) / price_range) * chart_area.height
            pygame.draw.circle(self.display, YELLOW, (int(x), int(y)), 8, 3)

        # Draw chart border
        pygame.draw.rect(self.display, BLACK, chart_area, 2)

    def _draw_info_panel(self):
        """Draw information panel"""
        y_offset = self.h - 140

        # Current info
        current_price = self.get_current_price()
        info_texts = [
            f"Step: {self.current_step}/{len(self.df)-1}",
            f"Price: ${current_price:.6f}",
            f"Balance: ${self.balance:.4f}",
            f"Position: {self.position.name}",
            f"Total Profit: ${self.total_profit:.4f}",
            f"Fee: {self.fee_percentage*100:.1f}% + ${self.fee_fixed:.3f}",
        ]

        if self.position == Position.LONG:
            # Calculate unrealized P&L including fees
            gross_value = self.coins_held * current_price
            sell_fee = self.calculate_transaction_fee(gross_value)
            net_value = gross_value - sell_fee
            unrealized_pnl = net_value - self.risk_amount

            info_texts.append(f"Coins Held: {self.coins_held:.6f}")
            info_texts.append(f"Entry Price: ${self.entry_price:.6f}")
            info_texts.append(f"Unrealized P&L: ${unrealized_pnl:.4f}")
            info_texts.append(f"Est. Sell Fee: ${sell_fee:.4f}")

        for i, text in enumerate(info_texts):
            rendered_text = font.render(text, True, BLACK)
            self.display.blit(rendered_text, (10, y_offset + i * 25))


# Example usage and testing
if __name__ == '__main__':
    # Create sample data for testing
    import numpy as np

    # Generate sample OHLCV data
    np.random.seed(42)
    n_periods = 1000

    # Generate realistic price movement
    returns = np.random.normal(0, 0.02, n_periods)
    prices = [100]  # Start at $100

    for ret in returns:
        prices.append(prices[-1] * (1 + ret))

    # Create OHLCV data
    data = []
    for i in range(len(prices)-1):
        base_price = prices[i]
        close_price = prices[i+1]

        high = max(base_price, close_price) * (1 + abs(np.random.normal(0, 0.01)))
        low = min(base_price, close_price) * (1 - abs(np.random.normal(0, 0.01)))
        volume = np.random.randint(1000, 10000)

        data.append({
            'open': base_price,
            'high': high,
            'low': low,
            'close': close_price,
            'volume': volume
        })

    df = pd.DataFrame(data)

    # Test the game
    game = CoinGameAI(df)

    print(f"Starting game with balance: ${game.balance:.4f}")
    print(f"Transaction fees: {game.fee_percentage*100:.1f}% + ${game.fee_fixed:.3f}")

    # Simple test loop
    for i in range(100):
        state = game.get_state()
        # Random action for testing
        action = [0, 0, 0]
        action[np.random.randint(0, 3)] = 1

        reward, done, profit = game.play_step(action)

        if done:
            print(f"Game finished! Total profit: ${profit:.4f}")
            print(f"Total trades executed: {len(game.trades)}")
            if game.trades:
                total_fees = sum(trade.get('fee', 0) for trade in game.trades)
                print(f"Total fees paid: ${total_fees:.4f}")
            break

    pygame.quit()
